<template>
    <div>
        <el-card shadow="hover">
            <el-input
                clearable
                size="mini"
               
                class="m-r-10"
                style="width: 180px"
                v-model="query.title"
                placeholder="请输入礼品卡名称"
            />
            <el-select
                v-model="query.type"
                size="mini"
                class="m-r-10"
                filterable
                clearable
                placeholder="请选择礼品卡类型"
            >
                <el-option
                    v-for="item in giftTypeList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
            </el-select>
            <el-button type="success" @click="search" size="mini"
                >查询
            </el-button>
            <el-button type="primary" size="mini" @click="openDialog"
                >新增</el-button
            >
        </el-card>
        <el-card shadow="hover">
            <el-table
                size="mini"
                border
                stripe
                :data="DataList"
                style="width: 100%"
            >
                <el-table-column
                    label="ID"
                    prop="id"
                    width="80"
                    align="center"
                />
                <el-table-column
                    label="礼品卡类型"
                    align="center"
                    prop="type"
                    min-width="100"
                   
                > 
                <template slot-scope="{ row }">
                    {{ row.type === 2 ? "虚拟卡" : "实物卡" }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="礼品卡名称"
                    align="center"
                    prop="title"
                    min-width="100"
                   
                />
               
                <el-table-column
                    label="礼品卡价值"
                    prop="recharge_amount"
                    align="center"
                    width="100"
                />
                <el-table-column
                    label="备注"
                    prop="remark"
                    min-width="200"
                    show-overflow-tooltip
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    label="创建人"
                    prop="create_name"
                    min-width="120"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    label="创建日期"
                    prop="created_time"
                    width="150"
                   
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    label="操作"
                    fixed="right"
                    align="center"
                    width="80"
                >
                    <template slot-scope="{ row }">
                        <el-button
                            type="text"
                            size="mini"
                            :style="{ color: row.status == 1 ? '#F56C6C' : '#67C23A' }"
                            @click="handleStatusChange(row)"
                            > {{ row.status == 1 ? "禁用" : "启用" }}</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <!-- 分页 -->
        <div class="pagination-block">
            <el-pagination
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog
            title="新增礼品卡"
            :visible.sync="dialogVisible"
            center
            :before-close="close"
            :close-on-click-modal="false"
            width="500px"
        >
        <el-form :rules="rules" ref="addref" size="mini" :model="formData" label-width="100px">
                <el-form-item label="礼品卡类型" prop="type">
                    <el-radio-group v-model="formData.type" style="margin-top: 8px;">
                        <el-radio :label="2">虚拟卡</el-radio>
                        <el-radio :label="3">实体卡</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="礼品卡名称" prop="title">
                    <el-input class="w-large" size="mini" placeholder="请输入名称" v-model="formData.title"></el-input>
                </el-form-item>
                <el-form-item label="金额" prop="price">
                    <el-input class="w-large"  size="mini" v-model="formData.price"></el-input> 元
                </el-form-item>
                <el-form-item label="封面图" prop="background">
                    <vos-oss
                    v-if="dialogVisible"
                            ref="vosOssRef1"
                            list-type="picture-card"
                            :showFileList="true"
                            :dir="dir"
                            :file-list="fileList"
                            :limit="1"
                            
                        >
                            <i class="el-icon-plus" />
                        </vos-oss>
                       
                </el-form-item>
                <el-form-item label="备注" prop="introduction">
                    <el-input class="w-large" v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="footer-dialog">
               
                <el-button type="primary" @click="handleEditSave">创建</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import VosOss from "vos-oss";
export default {
    components: {
        VosOss,
    },
    data() {
        const checkFormImage = (rues, value, callback) => {
            if (this.fileList.length == 0) {
                callback(new Error("请上传图片"));
            } else {
                callback();
            }
        };
        return {
            dir: "vinehoo/giftcard/",
            giftTypeList: [
                {
                    id: 2,
                    name: "虚拟卡",
                },
                {
                    id: 3,
                    name: "实物卡",
                },
            ],
            dialogVisible: false,
           
            DataList: [],
            query: {
                page: 1,
                limit: 10,
                type: "",
                title: "",
                
            },
            formData:{
                remark:'',
                title:'',
                type:2,
                price:'',
                background:'',
                recharge_amount:0,
                gift_amount:0,
            },
            rules: {
                title: [
              {
                  required: true,
                  trigger: "blur",
                  message: "请输入名称",
              },
              ],
               price: [
              {
                  required: true,
                  trigger: "blur",
                  message: "请输入金额",
              },
              ],
              background: [
                {
                    validator: checkFormImage,
                    trigger: "change",
                    required: true,
                },
                ],
             
          },
            fileList: [],
            detail: {},
            total: 0,
        };
    },
    mounted() {
       
        this.getData();
    },
    methods: {
       
        getData() {
            
            const data = {
                ...this.query,
            };
            
            this.$request.giftcard.getGiftCardList(data).then((res) => {
                if (res.data.error_code == 0) {
                    this.DataList = res.data.data.list;
                    this.total = res.data.data.total;
                }
            });
        },
        // 处理状态变更
        handleStatusChange(row) {
            const newStatus = row.status == 1 ? 2 : 1;
            const actionText = newStatus == 1 ? "启用" : "禁用";

            this.$confirm(`确定要${actionText}这个礼品卡吗？`, `确认${actionText}`, {
                confirmButtonText: `确定${actionText}`,
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const data = {
                    id: row.id,
                    status: newStatus
                };

                this.$request.giftcard.giftCardsCardStatusChange(data).then((res) => {
                    if (res.data.error_code == 0) {
                        this.$message.success(`${actionText}成功`);
                        this.getData();
                    }
                });
            }).catch(() => {
                // 用户取消操作
            });
        },
        // 打开新增对话框
        openDialog() {
            // 重置表单数据
            this.formData = this.$options.data().formData;
            // 清空文件列表
            this.fileList = [];
            // 打开对话框
            this.dialogVisible = true;
            // 下一个tick重置表单验证（确保DOM已更新）
            this.$nextTick(() => {
                if (this.$refs.addref) {
                    this.$refs.addref.resetFields();
                }
            });
        },
        close() {
            this.dialogVisible = false;
            // 重置表单数据
            this.formData = this.$options.data().formData;
            // 清空文件列表
            this.fileList = [];
            // 重置表单验证
            if (this.$refs.addref) {
                this.$refs.addref.resetFields();
            }
        },
        handleEditSave() {
            this.$refs.addref.validate((valid) => {
            if (valid) {
                this.formData.recharge_amount = this.formData.price;
                this.$request.giftcard.createGiftCard(this.formData).then((res) => {
                    if (res.data.error_code == 0) {
                        this.$message.success("创建成功");
                        this.dialogVisible = false;
                        // 重置表单数据
                        this.formData = this.$options.data().formData;
                        // 清空文件列表
                        this.fileList = [];
                        // 重置表单验证
                        if (this.$refs.addref) {
                            this.$refs.addref.resetFields();
                        }
                        this.getData();
                    }
                });
            }
            });
          
        },
        search() {
            this.query.page = 1;
            this.getData();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getData();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getData();
        },
    },
};
</script>
<style></style>
<style lang="scss" scoped>
.article-main {
    margin: 10px 0;

    /deep/ .el-table .warning-row {
        background: oldlace;
    }

    /deep/ .el-table .danger-row {
        background: oldlace;
    }

    /deep/ .el-table .success-row {
        background: #f0f9eb;
    }
}

.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
}

.article-form {
    & > div {
        display: inline-block;
    }
}
</style>
