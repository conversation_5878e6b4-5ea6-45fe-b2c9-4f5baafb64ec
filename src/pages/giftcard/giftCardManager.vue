<template>
    <div>
        <el-card shadow="hover">
            <el-input
                clearable
                size="mini"
                class="m-r-10"
                style="width: 180px"
                v-model="query.card_no"
                placeholder="礼品卡号"
            />
            <el-select
                v-model="query.status"
                size="mini"
                class="m-r-10"
                clearable
                placeholder="请选择状态"
                style="width: 120px"
            >
                <el-option
                    v-for="(name, value) in statusMap"
                    :key="value"
                    :label="name"
                    :value="parseInt(value)"
                />
            </el-select>
            <el-select
                v-model="query.type"
                size="mini"
                class="m-r-10"
                clearable
                placeholder="请选择类型"
                style="width: 120px"
            >
                <el-option label="虚拟卡" :value="1" />
                <el-option label="实体卡" :value="2" />
            </el-select>
            <!-- <el-select
                v-model="query.source"
                size="mini"
                class="m-r-10"
                clearable
                placeholder="请选择来源"
                style="width: 120px"
            >
                <el-option label="自购" :value="1" />
                <el-option label="被赠" :value="2" />
            </el-select> -->
            <el-date-picker
                v-model="dateRange"
                type="datetimerange"
                size="mini"
                class="m-r-10"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="timestamp"
                 :default-time="['00:00:00', '23:59:59']"
                @change="handleDateChange"
            />
            <el-button type="success" @click="search" size="mini">查询</el-button>
            <el-button @click="resetSearch" size="mini">重置</el-button>
        </el-card>
        <el-card shadow="hover">
            <el-table
                size="mini"
                border
                stripe
                :data="DataList"
                style="width: 100%"
            >
                <el-table-column
                    label="礼品卡类型"
                    prop="type"
                    width="100"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        {{ row.type === 1 ? "虚拟卡" : "实体卡" }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="礼品卡号"
                    prop="card_no"
                    min-width="200"
                    align="center"
                />
                <el-table-column
                    label="礼品卡面值"
                    prop="amount"
                    width="120"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        {{ row.amount }}
                        <!-- <span v-if="row.bonus && parseFloat(row.bonus) > 0" style="color: #E6A23C">
                            (+{{ row.bonus }})
                        </span> -->
                    </template>
                </el-table-column>
                <el-table-column
                    label="礼品卡状态"
                    prop="status"
                    width="100"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        <span :style="{ color: getStatusColor(row.status) }">
                            {{ getStatusName(row.status) }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="使用时间"
                    prop="use_time"
                    width="180"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        <span v-if="row.status === 2">{{ row.use_time }}</span>
                        <span v-else-if="row.status === 5">{{ row.repeal_time }}</span>
                        <span v-else>-</span>
                        
                    </template>
                </el-table-column>
                <el-table-column
                    label="购买人"
                    prop="buyer_name"
                    width="150"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        {{ row.buyer_name || '-' }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="使用人"
                    prop="owner_name"
                    width="150"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        <span v-if="row.status === 2">{{ row.owner_name }}</span>
                        <span v-else-if="row.status === 5">{{ row.repeal_name }}</span>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="操作"
                    fixed="right"
                    align="center"
                    width="80"
                >
                    <template slot-scope="{ row }">
                        <el-button
                            v-if="row.status === 1 "
                            type="text"
                            size="mini"
                            style="color: #F56C6C"
                            @click="handleRepeal(row)"
                        >
                            作废
                        </el-button>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <!-- 分页 -->
        <div class="pagination-block">
            <el-pagination
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- 作废对话框 -->
        <el-dialog
            title="作废礼品卡"
            :visible.sync="repealDialogVisible"
            center
            :close-on-click-modal="false"
            width="500px"
        >
            <div style="margin-bottom: 20px; padding: 15px; background-color: #fff6f6; border: 1px solid #fde2e2; border-radius: 4px;">
                <!-- <p style="margin: 0; color: #f56c6c; font-weight: bold;">⚠️ 警告</p> -->
                <p style="margin: 5px 0 0 0; color: #606266;">作废后的礼品卡将无法恢复，请谨慎操作！</p>
            </div>
            <el-form size="mini" :model="repealForm" label-width="90px">
                <el-form-item label="礼品卡号">
                    <span style="font-weight: bold;">{{ repealForm.card_no }}</span>
                </el-form-item>
                <el-form-item label="礼品卡面值" v-if="currentRepealCard">
                    <span>{{ currentRepealCard.amount }}</span>
                    <span v-if="currentRepealCard.bonus && parseFloat(currentRepealCard.bonus) > 0" style="color: #E6A23C">
                        (+{{ currentRepealCard.bonus }})
                    </span>
                </el-form-item>
                <el-form-item label="作废备注" prop="remark" required>
                    <el-input
                        v-model="repealForm.remark"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入作废原因（必填）"
                        maxlength="200"
                        show-word-limit
                    />
                </el-form-item>
            </el-form>
            <div slot="footer" class="footer-dialog">
                <el-button @click="repealDialogVisible = false">取消</el-button>
                <el-button type="danger" @click="confirmRepeal" :loading="repealLoading">确认作废</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            DataList: [],
            query: {
                page: 1,
                limit: 10,
                card_no: "",
                status: "",
                type: "",
                source: "",
                start_time: "",
                end_time: "",
            },
            dateRange: null,
            total: 0,
            repealDialogVisible: false,
            repealLoading: false,
            currentRepealCard: null,
            repealForm: {
                card_no: "",
                remark: ""
            },
            // 状态映射
            statusMap: {
                1: "待使用",
                2: "已使用",
                3: "退款中",
                4: "已退款",
                5: "已作废"
            }
        };
    },
    mounted() {
        this.getData();
    },
    methods: {
        getData() {
            const data = {
                ...this.query,
            };
            // 过滤空值参数
            Object.keys(data).forEach(key => {
                if (data[key] === "" || data[key] === null || data[key] === undefined) {
                    delete data[key];
                }
            });

            this.$request.giftcard.getgfListMannger(data).then((res) => {
                if (res.data.error_code == 0) {
                    this.DataList = res.data.data.list;
                    this.total = res.data.data.total;
                }
            });
        },
        search() {
            this.query.page = 1;
            this.getData();
        },
        resetSearch() {
            this.query = {
                page: 1,
                limit: 10,
                card_no: "",
                status: "",
                type: "",
                source: "",
                start_time: "",
                end_time: "",
            };
            this.dateRange = null;
            this.getData();
        },
        handleDateChange(val) {
            if (val && val.length === 2) {
                // 将时间戳转换为秒级时间戳
                this.query.start_time = Math.floor(val[0] / 1000);
                this.query.end_time = Math.floor(val[1] / 1000);
            } else {
                this.query.start_time = "";
                this.query.end_time = "";
            }
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getData();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getData();
        },
        // 获取状态名称
        getStatusName(status) {
            return this.statusMap[status] || "未知状态";
        },
        // 获取状态颜色
        getStatusColor(status) {
            const colorMap = {
                1: "#409EFF", // 待使用 - 蓝色
                2: "#67C23A", // 已使用 - 绿色
                3: "#E6A23C", // 退款中 - 橙色
                4: "#909399", // 已退款 - 灰色
                5: "#F56C6C"  // 已作废 - 红色
            };
            return colorMap[status] || "#606266";
        },
        // 处理作废操作
        handleRepeal(row) {
            this.currentRepealCard = row;
            this.repealForm.card_no = row.card_no;
            this.repealForm.remark = "";
            this.repealDialogVisible = true;
        },
        // 确认作废
        confirmRepeal() {
            if (!this.repealForm.remark.trim()) {
                this.$message.warning("请输入作废原因");
                return;
            }
            this.repealLoading = true;
                this.$request.giftcard.repealGiftCards(this.repealForm).then((res) => {
                    if (res.data.error_code == 0) {
                        this.$message.success("作废成功");
                        this.repealDialogVisible = false;
                        this.getData();
                    }
                }).finally(() => {
                    this.repealLoading = false;
                });
            // this.$confirm('确定要作废这张礼品卡吗？作废后将无法恢复！', '确认作废', {
            //     confirmButtonText: '确定作废',
            //     cancelButtonText: '取消',
            //     type: 'warning',
            //     confirmButtonClass: 'el-button--danger'
            // }).then(() => {
               
            // }).catch(() => {
            //     // 用户取消操作
            // });
        }
    },
};
</script>

<style lang="scss" scoped>
.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
}

.m-r-10 {
    margin-right: 10px;
}

.footer-dialog {
    text-align: right;
}
</style>
