<template>
    <div>
        <el-card shadow="hover">
            <el-input
                clearable
                size="mini"
                class="m-r-10"
                style="width: 180px"
                v-model="query.uid"
                placeholder="用户UID"
            />
            <el-input
                clearable
                size="mini"
                class="m-r-10"
                style="width: 180px"
                v-model="query.nickname"
                placeholder="用户昵称"
            />
            <el-date-picker
                v-model="dateRange"
                type="datetimerange"
                size="mini"
                class="m-r-10"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd HH:mm:ss"
                 :default-time="['00:00:00', '23:59:59']"
                value-format="timestamp"
                @change="handleDateChange"
            />
            <el-button type="success" @click="search" size="mini">查询</el-button>
        </el-card>
        <el-card shadow="hover">
            <el-table
                size="mini"
                border
                stripe
                :data="DataList"
                style="width: 100%"
            >
                <el-table-column
                    label="用户昵称(UID)"
                    prop="nickname"
                    min-width="150"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        {{ row.nickname }} ({{ row.uid }})
                    </template>
                </el-table-column>
                <el-table-column
                    label="类型"
                    align="center"
                    prop="type"
                    width="100"
                >
                    <template slot-scope="{ row }">
                        <span :style="{ color: row.type === 1 ? '#67C23A' : '#F56C6C' }">
                            {{ row.type === 1 ? "增加余额" : "减少余额" }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="原因"
                    align="center"
                    prop="operation_type"
                    min-width="120"
                >
                    <template slot-scope="{ row }">
                        <span :style="{ color: getOperationTypeColor(row.operation_type) }">
                            {{ getOperationTypeName(row.operation_type) }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="金额"
                    prop="amount"
                    align="center"
                    width="150"
                >
                    <template slot-scope="{ row }">
                        <span :style="{ color: row.type === 1 ? '#67C23A' : '#F56C6C' }">
                            {{ formatAmount(row) }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="时间"
                    prop="change_time"
                    width="180"
                    align="center"
                />
                <el-table-column
                    label="当前余额（赠金）"
                    align="center"
                    width="150"
                >
                    <template slot-scope="{ row }">
                        {{ row.after_recharge_amount }} ({{ row.after_bonus_amount }})
                    </template>
                </el-table-column>
                <el-table-column
                    label="操作人"
                    prop="change_name"
                    width="100"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        {{ row.change_name  }}
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <!-- 分页 -->
        <div class="pagination-block">
            <el-pagination
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            DataList: [],
            query: {
                page: 1,
                limit: 10,
                uid: "",
                nickname: "",
                start_time: "",
                end_time: "",
            },
           
            dateRange: null,
            total: 0,
            // 操作类型映射
            operationTypeMap: {
                1: "余额充值",
                2: "虚拟卡充值",
                3: "实体卡充值",
                4: "官方补偿",
                5: "商品购买",
                6: "订单余额化",
                7: "手动扣减"
            }
        };
    },
    mounted() {
        this.getData();
    },
    methods: {
        getData() {
            const data = {
                ...this.query,
            };
            // 过滤空值参数
            Object.keys(data).forEach(key => {
                if (data[key] === "" || data[key] === null || data[key] === undefined) {
                    delete data[key];
                }
            });

            this.$request.giftcard.getbalanceRecordList(data).then((res) => {
                if (res.data.error_code == 0) {
                    this.DataList = res.data.data.list;
                    this.total = res.data.data.total;
                }
            });
        },
        search() {
            this.query.page = 1;
            this.getData();
        },
        handleDateChange(val) {
            if (val && val.length === 2) {
                // 将时间戳转换为秒级时间戳（如果接口需要秒级）
                this.query.start_time = Math.floor(val[0] / 1000);
                this.query.end_time = Math.floor(val[1] / 1000);
            } else {
                this.query.start_time = "";
                this.query.end_time = "";
            }
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getData();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getData();
        },
        // 获取操作类型名称
        getOperationTypeName(type) {
            return this.operationTypeMap[type] || "未知操作";
        },
        // 获取操作类型颜色
        getOperationTypeColor(type) {
            const colorMap = {
                1: "#67C23A", // 余额充值 - 绿色
                2: "#409EFF", // 虚拟卡充值 - 蓝色
                3: "#409EFF", // 实体卡充值 - 蓝色
                4: "#E6A23C", // 官方补偿 - 橙色
                5: "#F56C6C", // 商品购买 - 红色
                6: "#909399", // 订单余额化 - 灰色
                7: "#F56C6C"  // 手动扣减 - 红色
            };
            return colorMap[type] || "#606266";
        },
        // 格式化金额显示
        formatAmount(row) {
            const rechargeAmount = parseFloat(row.recharge_amount || 0);
            const bonusAmount = parseFloat(row.bonus_amount || 0);
            const prefix = row.type === 1 ? "+" : "-";

            if (bonusAmount > 0) {
                return `${prefix}${rechargeAmount} (${prefix}${bonusAmount})`;
            } else {
                return `${prefix}${rechargeAmount}`;
            }
        }
    },
};
</script>

<style lang="scss" scoped>
.article-main {
    margin: 10px 0;

    /deep/ .el-table .warning-row {
        background: oldlace;
    }

    /deep/ .el-table .danger-row {
        background: oldlace;
    }

    /deep/ .el-table .success-row {
        background: #f0f9eb;
    }
}

.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
}

.m-r-10 {
    margin-right: 10px;
}
</style>
