<template>
    <div>
        <el-card shadow="hover">
           
            <el-button type="primary" size="mini" @click="openDialog"
                >新增</el-button
            >
        </el-card>
        <el-card shadow="hover">
            <el-table
                size="mini"
                border
                stripe
                :data="DataList"
                style="width: 100%"
            >
                <el-table-column
                    label="ID"
                    prop="id"
                    width="80"
                    align="center"
                />
                <el-table-column
                    label="充值金额"
                    align="center"
                    prop="price"
                    min-width="100"
                />
                <el-table-column
                    label="赠送"
                    prop="gift_amount"
                    align="center"
                    width="100"
                />
                <el-table-column
                    label="备注"
                    prop="remark"
                    min-width="200"
                    show-overflow-tooltip
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    label="创建人"
                    prop="create_name"
                    min-width="120"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    label="创建日期"
                    prop="created_time"
                    width="150"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    label="操作"
                    fixed="right"
                    align="center"
                    width="80"
                >
                    <template slot-scope="{ row }">
                        <el-button
                            type="text"
                            size="mini"
                            :style="{ color: row.status == 1 ? '#F56C6C' : '#67C23A' }"
                            @click="handleStatusChange(row)"
                            > {{ row.status == 1 ? "禁用" : "启用" }}</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <!-- 分页 -->
        <div class="pagination-block">
            <el-pagination
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog
            title="新增充值面值"
            :visible.sync="dialogVisible"
            center
            :before-close="close"
            :close-on-click-modal="false"
            width="40%"
        >
        <el-form :rules="rules"  ref="addref" size="mini" :model="formData" label-width="90px">
                <el-form-item label="金额" prop="price">
                    <el-input class="w-large"  size="mini" v-model="formData.price" placeholder="请输入金额"></el-input> 元
                </el-form-item>
                <el-form-item label="赠送" prop="gift_amount">
                    <el-input class="w-large"  size="mini" v-model="formData.gift_amount" placeholder="请输入金额"></el-input> 元
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input class="w-large" v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注"></el-input>
                </el-form-item >
            </el-form>
            <div slot="footer" class="footer-dialog">
                <el-button type="primary" @click="handleEditSave">创建</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            dialogVisible: false,
            DataList: [],
            query: {
                page: 1,
                limit: 10,
                type: 1, // 固定为直接充值类型
                title: "",
            },
            formData:{
                remark:'',
                title:'',
                type: 1, // 固定为直接充值类型
                price:'',
                background:'',
                recharge_amount:0,
                gift_amount:0,
            },
            rules: {
               
                price: [
               {
                   required: true,
                   trigger: "blur",
                   message: "请输入金额",
               },
               ],
               gift_amount: [
               {
                   required: true,
                   trigger: "blur",
                   message: "请输入赠金",
               },
               ],
              
           },
            detail: {},
            total: 0,
        };
    },
    mounted() {
        this.getData();
    },
    methods: {
        getData() {
            const data = {
                ...this.query,
            };

            this.$request.giftcard.getGiftCardList(data).then((res) => {
                if (res.data.error_code == 0) {
                    this.DataList = res.data.data.list;
                    this.total = res.data.data.total;
                }
            });
        },
        // 处理状态变更
        handleStatusChange(row) {
            const newStatus = row.status == 1 ? 2 : 1;
            const actionText = newStatus == 1 ? "启用" : "禁用";

            this.$confirm(`确定要${actionText}这个礼品卡吗？`, `确认${actionText}`, {
                confirmButtonText: `确定${actionText}`,
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const data = {
                    id: row.id,
                    status: newStatus
                };

                this.$request.giftcard.giftCardsCardStatusChange(data).then((res) => {
                    if (res.data.error_code == 0) {
                        this.$message.success(`${actionText}成功`);
                        this.getData();
                    }
                });
            }).catch(() => {
                // 用户取消操作
            });
        },
        // 打开新增对话框
        openDialog() {
            // 重置表单数据
            this.formData = this.$options.data().formData;
            // 打开对话框
            this.dialogVisible = true;
            // 下一个tick重置表单验证（确保DOM已更新）
            this.$nextTick(() => {
                if (this.$refs.addref) {
                    this.$refs.addref.resetFields();
                }
            });
        },
        close() {
            this.dialogVisible = false;
            // 重置表单数据
            this.formData = this.$options.data().formData;
            // 重置表单验证
            if (this.$refs.addref) {
                this.$refs.addref.resetFields();
            }
        },
        handleEditSave() {
            this.$refs.addref.validate((valid) => {
            if (valid) {
                // 设置充值金额等于价格
            this.formData.recharge_amount = this.formData.price;
            // 设置标题
            this.formData.title = `${this.formData.price}元充值卡`;

            this.$request.giftcard.createGiftCard(this.formData).then((res) => {
                if (res.data.error_code == 0) {
                    this.$message.success("创建成功");
                    this.dialogVisible = false;
                    // 重置表单数据
                    this.formData = this.$options.data().formData;
                    // 重置表单验证
                    if (this.$refs.addref) {
                        this.$refs.addref.resetFields();
                    }
                    this.getData();
                }
            });
            }
            });
           
        },
        search() {
            this.query.page = 1;
            this.getData();
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getData();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getData();
        },
    },
};
</script>
<style></style>
<style lang="scss" scoped>
.article-main {
    margin: 10px 0;

    /deep/ .el-table .warning-row {
        background: oldlace;
    }

    /deep/ .el-table .danger-row {
        background: oldlace;
    }

    /deep/ .el-table .success-row {
        background: #f0f9eb;
    }
}

.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
}

.article-form {
    & > div {
        display: inline-block;
    }
}
</style>