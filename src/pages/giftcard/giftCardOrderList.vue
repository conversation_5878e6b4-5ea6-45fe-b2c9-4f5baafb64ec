<template>
    <div>
        <el-card shadow="hover">
            <el-input
                clearable
                size="mini"
                class="m-r-10"
                style="width: 180px"
                v-model="query.uid"
                placeholder="用户UID"
            />
            <el-input
                clearable
                size="mini"
                class="m-r-10"
                style="width: 180px"
                v-model="query.nickname"
                placeholder="用户昵称"
            />
            <el-input
                clearable
                size="mini"
                class="m-r-10"
                style="width: 180px"
                v-model="query.card_no"
                placeholder="礼品卡号"
            />
            <el-input
                clearable
                size="mini"
                class="m-r-10"
                style="width: 180px"
                v-model="query.order_no"
                placeholder="订单号"
            />
            <el-select
                v-model="query.type"
                size="mini"
                class="m-r-10"
                clearable
                placeholder="请选择类型"
                style="width: 120px"
            >
               
                <el-option label="虚拟卡" :value="2" />
                <el-option label="实体卡" :value="3" />
            </el-select>
            <el-date-picker
                v-model="dateRange"
                type="daterange"
                size="mini"
                class="m-r-10"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                @change="handleDateChange"
            />
            <el-button type="success" @click="search" size="mini">查询</el-button>
            <el-button @click="resetSearch" size="mini">重置</el-button>
        </el-card>
        <el-card shadow="hover">
            <el-table
                size="mini"
                border
                stripe
                :data="DataList"
                style="width: 100%"
            >
                <el-table-column
                    label="用户昵称(UID)"
                    prop="nickname"
                    min-width="150"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        {{ row.nickname || '-' }} ({{ row.uid }})
                    </template>
                </el-table-column>
                <el-table-column
                    label="礼品卡类型"
                    prop="type_text"
                    width="100"
                    align="center"
                />
                <el-table-column
                    label="订单号"
                    prop="order_no"
                    min-width="180"
                    align="center"
                />
                <el-table-column
                    label="对应卡号"
                    prop="card_nos"
                    min-width="200"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        <div v-if="row.card_nos && row.card_nos.length > 0">
                            <div v-for="(cardNo, index) in row.card_nos" :key="index">
                                {{ cardNo }}
                            </div>
                        </div>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="数量"
                    prop="order_qty"
                    width="80"
                    align="center"
                />
                <el-table-column
                    label="订单状态"
                    prop="order_status_text"
                    width="100"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        <span :style="{ color: getOrderStatusColor(row.order_status) }">
                            {{ row.order_status_text }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="退款时间"
                    prop="refund_time"
                    width="180"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        {{ row.refund_time || '-' }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="购买时间"
                    prop="payment_time"
                    width="180"
                    align="center"
                />
                <el-table-column
                    label="支付金额"
                    prop="payment_amount"
                    width="100"
                    align="center"
                />
                <el-table-column
                    label="操作人"
                    prop="operator"
                    width="100"
                    align="center"
                />
                <el-table-column
                    label="操作"
                    fixed="right"
                    align="center"
                    width="80"
                >
                    <template slot-scope="{ row }">
                        <el-button
                            v-if="row.order_status === 3 && canRefund(row)"
                            type="text"
                            size="mini"
                            style="color: #F56C6C"
                            @click="handleRefund(row)"
                        >
                            退款
                        </el-button>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <!-- 分页 -->
        <div class="pagination-block">
            <el-pagination
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- 退款对话框 -->
        <el-dialog
            title="订单退款"
            :visible.sync="refundDialogVisible"
            center
            :close-on-click-modal="false"
            width="500px"
        >
            <div style="margin-bottom: 20px; padding: 15px; background-color: #fff6f6; border: 1px solid #fde2e2; border-radius: 4px;">
                <!-- <p style="margin: 0; color: #f56c6c; font-weight: bold;">⚠️ 警告</p> -->
                <p style="margin: 5px 0 0 0; color: #606266;">退款后订单将无法恢复，请谨慎操作！</p>
            </div>
            <el-form size="mini" :model="refundForm" label-width="90px">
                <el-form-item label="订单号">
                    <span style="font-weight: bold;">{{ refundForm.order_no }}</span>
                </el-form-item>
                <el-form-item label="支付金额" v-if="currentRefundOrder">
                    <span>{{ currentRefundOrder.payment_amount }}</span>
                </el-form-item>
                <el-form-item label="购买数量" v-if="currentRefundOrder">
                    <span>{{ currentRefundOrder.order_qty }}</span>
                </el-form-item>
                <el-form-item label="礼品卡号" v-if="currentRefundOrder && currentRefundOrder.card_nos && currentRefundOrder.card_nos.length > 0">
                    <el-checkbox-group v-model="refundForm.card_nos">
                        <div v-for="(cardNo, index) in currentRefundOrder.card_nos" :key="index" style="margin-bottom: 5px;">
                            <el-checkbox :label="cardNo">
                                {{ cardNo }}
                            </el-checkbox>
                        </div>
                    </el-checkbox-group>
                    <div style="margin-top: 10px; font-size: 12px; color: #909399;">
                        * 选择要退款的礼品卡号，不选择则退款整个订单
                    </div>
                </el-form-item>
                <el-form-item label="退款备注" prop="remarks">
                    <el-input
                        v-model="refundForm.remarks"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入退款原因"
                        maxlength="200"
                        show-word-limit
                    />
                </el-form-item>
            </el-form>
            <div slot="footer" class="footer-dialog">
                <el-button @click="refundDialogVisible = false">取消</el-button>
                <el-button type="danger" @click="confirmRefund" :loading="refundLoading">确认退款</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            DataList: [],
            query: {
                page: 1,
                limit: 10,
                uid: "",
                nickname: "",
                card_no: "",
                order_no: "",
                date_range: "",
                type: "",
            },
            dateRange: null,
            total: 0,
            refundDialogVisible: false,
            refundLoading: false,
            currentRefundOrder: null,
            refundForm: {
                order_no: "",
                card_nos: [],
                remarks: ""
            }
        };
    },
    mounted() {
        this.getData();
    },
    methods: {
        getData() {
            const data = {
                ...this.query,
            };
            // 过滤空值参数
            Object.keys(data).forEach(key => {
                if (data[key] === "" || data[key] === null || data[key] === undefined) {
                    delete data[key];
                }
            });

            this.$request.giftcard.getgfListOrderList(data).then((res) => {
                if (res.data.error_code == 0) {
                    this.DataList = res.data.data.list;
                    this.total = res.data.data.total;
                }
            });
        },
        search() {
            this.query.page = 1;
            this.getData();
        },
        resetSearch() {
            this.query = {
                page: 1,
                limit: 10,
                uid: "",
                nickname: "",
                card_no: "",
                order_no: "",
                date_range: "",
                type: "",
            };
            this.dateRange = null;
            this.getData();
        },
        handleDateChange(val) {
            if (val && val.length === 2) {
                this.query.date_range = `${val[0]},${val[1]}`;
            } else {
                this.query.date_range = "";
            }
        },
        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getData();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getData();
        },
        // 获取订单状态颜色
        getOrderStatusColor(status) {
            const colorMap = {
                1: "#E6A23C", // 待支付 - 橙色
                2: "#409EFF", // 已支付 - 蓝色
                3: "#67C23A", // 已完成 - 绿色
                4: "#F56C6C", // 已退款 - 红色
                5: "#909399"  // 已取消 - 灰色
            };
            return colorMap[status] || "#606266";
        },
        // 判断是否可以退款
        canRefund(row) {
            // 只有已完成的订单才能退款
            return row.order_status === 3;
        },
        // 处理退款操作
        handleRefund(row) {
            this.currentRefundOrder = row;
            this.refundForm.order_no = row.order_no;
            this.refundForm.card_nos = [];
            this.refundForm.remarks = "";
            this.refundDialogVisible = true;
        },
        // 确认退款
        confirmRefund() {
            if (!this.refundForm.remarks.trim()) {
                this.$message.warning("请输入退款原因");
                return;
            }

            // 构建退款数据
            const refundData = {
                order_no: this.refundForm.order_no,
                remarks: this.refundForm.remarks
            };

            // 如果选择了特定的礼品卡号，则添加到退款数据中
            if (this.refundForm.card_nos && this.refundForm.card_nos.length > 0) {
                refundData.card_nos = this.refundForm.card_nos;
            }

            let confirmMessage = '确定要退款这个订单吗？退款后将无法恢复！';
            if (refundData.card_nos && refundData.card_nos.length > 0) {
                confirmMessage = `确定要退款选中的 ${refundData.card_nos.length} 张礼品卡吗？退款后将无法恢复！`;
            }

            this.$confirm(confirmMessage, '确认退款', {
                confirmButtonText: '确定退款',
                cancelButtonText: '取消',
                type: 'warning',
                confirmButtonClass: 'el-button--danger'
            }).then(() => {
                this.refundLoading = true;
                this.$request.giftcard.giftCardsOrderRefund(refundData).then((res) => {
                    if (res.data.error_code == 0) {
                        this.$message.success("退款成功");
                        this.refundDialogVisible = false;
                        this.getData();
                    }
                }).finally(() => {
                    this.refundLoading = false;
                });
            }).catch(() => {
                // 用户取消操作
            });
        }
    },
};
</script>

<style lang="scss" scoped>
.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
}

.m-r-10 {
    margin-right: 10px;
}

.footer-dialog {
    text-align: right;
}
</style>
