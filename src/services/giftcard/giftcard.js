import axios from "axios";

function getGiftCardList(params) {
    // 礼品卡列表
    return axios({
        url: "/api/user/v3/giftCards/giftCardList",
        method: "GET",
        params
    });
}
//创建礼品卡
function createGiftCard(data) {
    return axios({
        url: "/api/user/v3/giftCards/createGiftCard",
        method: "post",
        data
    });
}
//礼品卡状态变动
function giftCardsCardStatusChange(data) {
    return axios({
        url: "/api/user/v3/giftCards/statusChange",
        method: "post",
        data
    });
}
//储值记录
function getbalanceRecordList(params) {
    
    return axios({
        url: "/api/user/v3/balance/balanceList",
        method: "GET",
        params
    });
}
//礼品卡管理
function getgfListMannger(params) {
    
    return axios({
        url: "/api/user/v3/giftCards/gfList",
        method: "GET",
        params
    });
}

//作废礼品卡
function repealGiftCards(data) {
    return axios({
        url: "/api/user/v3/giftCards/repeal",
        method: "post",
        data
    });
}
//礼品卡订单列表
function getgfListOrderList(params) {
    
    return axios({
        url: "/api/user/v3/giftCards/orderList",
        method: "GET",
        params
    });
}
//礼品卡退款
function giftCardsOrderRefund(data) {
    return axios({
        url: "/api/user/v3/giftCards/orderRefund",
        method: "post",
        data
    });
}

export default {
    getGiftCardList,
    createGiftCard,
    giftCardsCardStatusChange,
    getbalanceRecordList,
    getgfListMannger,
    repealGiftCards,
    getgfListOrderList,
    giftCardsOrderRefund
};
