<template>
    <div>
        <!-- begin #header -->
        <div class="header navbar-default" id="header">
            <!-- begin navbar-header -->
            <div class="navbar-header">
                <span class="navbar-brand" to="/dashboard/v2"
                    ><i
                        class="el-icon-s-home"
                        @click="selectPlatform"
                        style="
                            margin-right: 4px;
                            font-size: 22px;
                            cursor: pointer;
                        "
                    ></i>
                    <b>储值卡管理系统</b>
                </span>
            </div>
            <!-- end navbar-header -->

            <!-- begin header-nav -->
            <ul class="navbar-nav navbar-right">
                <!-- <li class="navbar-form">
                    <form name="search_form" v-on:submit="checkForm">
                        <div class="form-group">
                            <input
                                class="form-control"
                                placeholder="Enter keyword"
                                type="text"
                            />
                            <button class="btn btn-search" type="submit">
                                <i class="fa fa-search"></i>
                            </button>
                        </div>
                    </form>
                </li> -->
                <!-- <b-nav-item-dropdown
                    menu-class="media-list dropdown-menu-right"
                    no-caret
                    toggle-class="f-s-14"
                >
                    <template slot="button-content">
                        <i class="fa fa-bell"></i>
                        <span class="label">0</span>
                    </template>
                    <b-dropdown-header>NOTIFICATIONS (0)</b-dropdown-header>
                    <b-dropdown-item
                        class="text-center width-300"
                        href="javascript:;"
                    >
                        No notification found
                    </b-dropdown-item>
                </b-nav-item-dropdown> -->

                <b-nav-item-dropdown
                    class="dropdown navbar-user"
                    menu-class="dropdown-menu-right"
                    no-caret
                >
                    <template slot="button-content">
                        <img :src="userinfo.avatar" alt="" />
                        <span style="font-size: 14px" class=" d-md-inline">{{
                            userinfo.realname
                        }}</span>
                        <b class="caret"></b>
                    </template>

                    <b-dropdown-item @click="selectPlatform"
                        >切换系统</b-dropdown-item
                    >
                    <b-dropdown-divider></b-dropdown-divider>
                    <b-dropdown-item @click="logOut">退出登录</b-dropdown-item>
                </b-nav-item-dropdown>
                <li
                    class="divider d-none d-md-block"
                    v-if="pageOptions.pageWithTwoSidebar"
                ></li>
                <li
                    class="d-none d-md-block"
                    v-if="pageOptions.pageWithTwoSidebar"
                >
                    <a
                        class="f-s-14"
                        href="javascript:;"
                        v-on:click="toggleRightSidebarCollapsed"
                    >
                        <i class="fa fa-th"></i>
                    </a>
                </li>
            </ul>
            <!-- end header navigation right -->
        </div>
        <!-- end #header -->
    </div>
</template>

<script>
import PageOptions from "../../config/PageOptions.vue";
import Cookies from "js-cookie";
export default {
    name: "Header",
    components: {},
    data() {
        return {
            userinfo: {
                realname: "",
                avatar: "",
                title: ""
            },

            pageOptions: PageOptions
        };
    },
    mounted() {
        this.getUserInfo();
    },
    methods: {
        getUserInfo() {
            const userinfo = localStorage.getItem("userinfo");
            this.userinfo = JSON.parse(userinfo);
        },
        selectPlatform() {
            window.location.replace("/login/systems");
        },
        logOut() {
            window.location.replace("/login/login");
            Cookies.remove("token");
            Cookies.remove("uid");
        },
        toggleMobileSidebar() {
            this.pageOptions.pageMobileSidebarToggled = !this.pageOptions
                .pageMobileSidebarToggled;
        },
        toggleMobileRightSidebar() {
            this.pageOptions.pageMobileRightSidebarToggled = !this.pageOptions
                .pageMobileRightSidebarToggled;
        },
        toggleMobileTopMenu() {
            this.pageOptions.pageMobileTopMenu = !this.pageOptions
                .pageMobileTopMenu;
        },
        toggleMobileMegaMenu() {
            this.pageOptions.pageMobileMegaMenu = !this.pageOptions
                .pageMobileMegaMenu;
        },
        toggleRightSidebar() {
            this.pageOptions.pageRightSidebarToggled = !this.pageOptions
                .pageRightSidebarToggled;
        },
        toggleRightSidebarCollapsed() {
            this.pageOptions.pageRightSidebarCollapsed = !this.pageOptions
                .pageRightSidebarCollapsed;
        },
        checkForm: function(e) {
            e.preventDefault();
            this.$router.push({ path: "/extra/search" });
        }
    }
};
</script>
