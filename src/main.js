import Vue from "vue";
import VueX from "vuex";
import router from "./config/PageRoutes";
import <PERSON>ement<PERSON> from "element-ui";
import "element-ui/lib/theme-chalk/index.css";
import VueRouter from "vue-router";
import VueBootstrap from "bootstrap-vue";
import VuePanel from "./plugins/panel/";
import "./assets/css/default/app.min.css";
import "./scss/vue.scss";
import Cookies from "js-cookie";
import App from "./App.vue";
import store from "./store/index";
import "./services/axios";
import Message from "./tools/Message";
import zhLocale from "element-ui/lib/locale/lang/zh-CN";
import server from "./services/index";
Vue.prototype.$Message = Message.call;
Vue.prototype.cookies = Cookies;
Vue.prototype.$request = server;
Vue.use(VueX);
// wineparty/v3/order/list
Vue.use(ElementUI, { zhLocale });
Vue.use(VueRouter);
Vue.use(VueBootstrap);
const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
    return originalPush.call(this, location).catch(err => err);
};
router.beforeEach((to, from, next) => {
    if (to.meta.content) {
        let head = document.getElementByTagName("head");
        let meta = document.createElemnet("meta");
        document
            .querySelector('meta[name="keywords"]')
            .setAttribute("content", to.meta.content.keywords);
        document
            .querySelector('meta[name="description"]')
            .setAttribute("content", to.meta.content.description);
        meta.content = to.meta.content;
        head[0].appendChild(meta);
    }
    if (to.meta.title) {
        //路由发生变化时候修改页面中的title
        document.title = to.meta.title + " | Vinehoo OS";
    }
    next();
});
Vue.use(VuePanel);
new Vue({
    render: h => h(App),
    router,
    store
}).$mount("#app");
